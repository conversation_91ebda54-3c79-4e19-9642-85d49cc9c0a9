import React, { useEffect, useState } from 'react';
import { Select, TreeSelect } from 'antd';
import { TreeSelectProps } from 'antd/lib/tree-select';
import { nsApiManagerAllGroup } from '@/remote';
import { debounce } from 'lodash';
import { TreeSelect_onDropdownVisibleChange } from '@libs/utils';
import { KDEV_API_GROUP } from '@/pages/ApiManager/apiManager.config';

interface IProps extends TreeSelectProps<number> {
    isFilterDefaultGroup?: boolean;
    projectId?: number; // 仓库ID过滤仓库目录使用
    getAllGroup?: boolean
}

function RepoGroupSelect({ isFilterDefaultGroup, projectId, getAllGroup = true, ...props }: IProps) {
    const [repoGroupList, setRepoGroupList] = useState<nsApiManagerAllGroup.IOption[]>([]);
    const [loading, setLoading] = useState(false);
    const [treeDefaultExpandedKeys, setTreeDefaultExpandedKeys] = useState<number[]>([]);

    const getRepoGroupList = async (key: string = '') => {
        setLoading(true);
        try {
            const res = await nsApiManagerAllGroup.remote({
                key,
                projectId,
                currentGroupId: getAllGroup ?
                    undefined :
                    JSON.parse(localStorage.getItem(KDEV_API_GROUP) || '{}').groupId,
            });
            setRepoGroupList(res.list);
            setLoading(false);
        } catch {
            setLoading(false);
        }
    };

    const onSearchGroup = debounce((key: string) => {
        getRepoGroupList(key);
    }, 300);

    useEffect(() => {
        getRepoGroupList();
    }, []);

    // 默认展开
    useEffect(() => {
        if (props.value) {
            const keys = nsApiManagerAllGroup.findNodeAndAncestors(repoGroupList, props.value);
            setTreeDefaultExpandedKeys(keys);
        }
    }, [props.value, repoGroupList]);

    function helper(group, id) {
        if (group.groupId === id) {
            return true;
        } else {
            for (const element of group.children) {
                if (helper(element, id)) {
                    return true;
                }
            }
        }
    }
    const searchAncestors = (val: number) => {
        for (const element of repoGroupList) {
            if (helper(element, val)) {
                return element
            }
        }
    }

    return (
        <TreeSelect
            treeData={repoGroupList}
            loading={loading}
            placeholder="请选择所属分组"
            allowClear
            showSearch
            dropdownMatchSelectWidth={true}
            // showArrow
            // suffixIcon={<KdevIconFont id={common_system_arrowxia} style={{color: '#898A8C'}}/>}
            multiple={false}
            onSearch={onSearchGroup}
            filterTreeNode={false}
            onClear={getRepoGroupList}
            treeDefaultExpandedKeys={treeDefaultExpandedKeys}
            onDropdownVisibleChange={TreeSelect_onDropdownVisibleChange}
            {...props}
            onChange={(value, label, extra) => {
                props.onChange && props.onChange(value, label, extra, searchAncestors(value));
            }}
        />
    );
}

export { RepoGroupSelect };
