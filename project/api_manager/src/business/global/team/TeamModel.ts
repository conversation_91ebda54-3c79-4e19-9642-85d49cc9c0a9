import { AViewModel } from 'libs';
import { observable } from 'mobx';
import { nsAresPermissionUsersGet } from '@/remote';
import { initLogger, initFeedBack, uploadClickEvent, ECLICK_POINT, initProductStudio, radar, ModuleQuery } from '@/index.config';
import { router } from '@libs/mvvm';
import { pushKey } from '@/index.config/tools';
import { fmp, getFmpRouterKeys } from 'kdev-fe-common/src/shared/common/fmpReport';

const KDEV_USER_INFO = 'KDEV_USER_INFO';
export class TeamModel extends AViewModel {
    @observable public userId: any = -1;
    @observable public userName: string = '';
    @observable public chineseName: string = '';
    @observable public photo: string = '';
    @observable public email: string = '';
    @observable public loadingList: boolean = false;
    public getUserPromise?: Promise<nsAresPermissionUsersGet.IReturn>;

    constructor(p?: Partial<TeamModel>) {
        super();
        if (p) {
            this.setFields(p);
        }
    }

    public async loadInfo() {
        this.setFields({ loadingList: true });
        try {
            this.getUserPromise = nsAresPermissionUsersGet.remote();
            let respRole;
            const storageUserInfo = localStorage.getItem(KDEV_USER_INFO);
            if (!storageUserInfo) {
                respRole = await this.getUserPromise;
            } else {
                respRole = JSON.parse(storageUserInfo);
                this.getUserPromise.then(res => {
                    localStorage.setItem(KDEV_USER_INFO, JSON.stringify({
                        userId: res.userId,
                        userName: res.userName,
                        chineseName: res.chineseName || '',
                        photo: res.photo,
                        email: res.email,
                    }))
                })
            }

            const userInfo = {
                userId: respRole.userId,
                userName: respRole.userName,
                chineseName: respRole.chineseName || '',
                photo: respRole.photo,
                email: respRole.email,
            }
            localStorage.setItem(KDEV_USER_INFO, JSON.stringify(userInfo))
            this.setFields(userInfo);
            initProductStudio(respRole.userName)
            initLogger(respRole.userName);

            fmp.setKeys(getFmpRouterKeys(radar, ModuleQuery));
            if (router.getQueryVal('tag') === 'kim') {
                uploadClickEvent({ record_type: ECLICK_POINT.KIM_NOTICE_ENTER });
                pushKey({}, ['tag']);
            }
            initFeedBack(respRole.userName);
        } finally {
            this.setFields({ loadingList: false });
        }
    }

    public getUserInfo() {
        return {
            userName: this.userName,
            userId: this.userId,
            photo: this.photo,
            chineseName: this.chineseName,
            email: this.email
        };
    }
}
