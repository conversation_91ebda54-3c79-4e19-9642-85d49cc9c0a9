import React from 'react';
import { DepartmentComponent } from './DepartmentComponent';
import { DepartmentComponentModel } from './DepartmentComponentModel';
import { isKDevPageV2 } from 'kdev-fe-common/src/component/KDevPageLayout/KDevPageLayout';
import { DepartmentComponent as  DepartmentComponentV2} from './DepartmentComponentV2';

const departmentComponentModel = new DepartmentComponentModel();
export const departmentComponent = {
    renderSelect(collapse?: boolean): React.ReactNode {
        if (isKDevPageV2) {
            return <DepartmentComponentV2 model={ departmentComponentModel } collapse={collapse || false}/>;
        }

        return <DepartmentComponent model={ departmentComponentModel } />;
    },
    departmentComponentModel
    // getEnvType(): number {
    //     return envSelectModel.envType;
    // }
};
