import React, { useRef, useEffect, useState } from 'react';
import css from './jsonAceEditor.less';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { AceEditor, KdevIconFont } from '@/business/commonComponents';
import { common_system_copy02, common_system_remove, common_system_delete, common_system_noticemian } from '@kid/enterprise-icon/icon/output/icons';
import { message, Tooltip } from 'antd';
import { Ace } from 'ace-builds';
interface Props {
    json: string;
    setJsonResp: (val: string) => void;
    formatJsonStr: () => void;
    errorLine: Ace.Annotation[];
    options?: string[];
    readonly?: boolean;
    className?: string[];
    style?: React.CSSProperties;
    from?: string;
}
const allOptions = ['copy', 'format', 'clear']

export function JsonAceEditor({ className = [], ...props }: Props) {
    let options: string[] = [];
    if (!props.options) {
        options = allOptions;
    }
    // 自动划到错误行
    const jsonEditorRef = useRef<AceEditor>(null);
    const [editorInstance, setEditorInstance] = useState<Ace.Editor | null>(null);
    useEffect(() => {
        if (props.errorLine && props.errorLine.length > 0 && editorInstance) {
            const firstErrorLine = props.errorLine?.[0]?.row ? props.errorLine[0].row - 1 : 0;
            editorInstance.scrollToLine(firstErrorLine, false, true);
        }
    }, [props.errorLine, editorInstance]);
    return (
        <div className={[css.aceEditorWrap, ...className].join(' ')} style={props.style}>
            {options.length ?
                <div className={css.optionsLine}>
                    {options.includes('copy') ?
                        <CopyToClipboard text={props.json || ''}>
                            <div className={css.optionWarp} onClick={() => message.success('复制成功')}>
                                <KdevIconFont className={css.icon} id={common_system_copy02} />
                                <span className={css.option}>复制</span>
                            </div>
                        </CopyToClipboard> : null
                    }
                    {options.includes('format') ?
                        <div className={css.optionWarp} onClick={() => { props.formatJsonStr() }}>
                            <KdevIconFont className={css.icon} id={common_system_remove} />
                            <span className={css.option}>格式化</span>
                        </div> : null
                    }
                    {options.includes('clear') ?
                        <div className={css.optionWarp} onClick={() => {
                            props.setJsonResp('')
                        }}>
                            <KdevIconFont className={css.icon} id={common_system_delete} />
                            <span className={css.option}>清空</span>
                        </div> : null
                    }
                    {
                        <span className={css.jsonTip}>
                            <KdevIconFont className={css.jsonCommonIcon} id={common_system_noticemian} />
                            支持 Json 注释，并自动为您将「行尾注释」转换到结构化列表中，
                            <Tooltip
                                placement="topRight"
                                color="#fff"
                                overlayStyle={{ whiteSpace: 'nowrap', maxWidth: 'none' }}
                                overlayInnerStyle={{
                                    boxShadow: '0 0 10px #ccc'
                                }}
                                title={
                                    <div style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        padding: '11px 8px',
                                    }}>
                                        <img src="/assets/json_common.png" alt="" style={{
                                            width: '488px',
                                            marginBottom: 16
                                        }} />
                                        <span style={{ color: '#252626' }}>行尾注释将同步到「结构化列表」中</span>
                                    </div>
                                }>
                                <span style={{
                                    color: '#326BFB'
                                }}>查看注释规范</span>
                            </Tooltip>
                        </span>
                    }
                </div> : null
            }
            <AceEditor
                readOnly={props.readonly}
                theme="xcode"
                width="100%"
                value={props.json}
                onChange={(val) => {
                    props.setJsonResp(val)
                }}
                showPrintMargin={false}
                annotations={props.errorLine}
                minLines={5}
                maxLines={50}
                ref={jsonEditorRef}
                onLoad={(editor) => {
                    setEditorInstance(editor);
                }}
            />
        </div>
    )
}
