import React, { useState, useRef, useEffect, useCallback } from 'react'
import css from './ProjectViewList.less';
import { Button, Input, Segmented, Tooltip, Modal, message } from 'antd';
import { pushKey } from '@/index.config/tools';
import { ViewTypeMap } from '../../ApiMenuEntry';
import { KdevIconFont, KEmpty } from '@/business/commonComponents';
import { common_system_switch, common_system_add, common_system_import, common_system_api, common_system_edit, common_system_pinxian, common_system_delete, common_system_pinmian, common_system_setting, common_system_time, common_system_search } from '@kid/enterprise-icon/icon/output/icons';
import classNames from 'classnames';
import { CreateProject, CreateProjectRef } from '../CreateProject/CreateProject';
import {
    nsMockManageApiManageMainApiProgram,
    nsMockManageApiManageMainApiProgramHot,
    nsMockManageApiManageMainApiProgramList,
    nsMockManageApiManageMainApiProgramPin,
    nsMockManageApiManageMainApiProgramUnpin,
    nsMockManageApiManageMainGroupUserDepartmentInfo,
    nsMockManageApiManageMainApiProgramSearch
} from '@/remote';
import { KDEV_API_GROUP, KDEV_API_SEARCH_GROUP } from '../../apiManager.config'
import { debounce } from 'lodash';
import { ERouter } from 'CONFIG';
import { router } from '@libs/mvvm';
import { AvatarList } from '@/business/AvatarList/AvatarList';

import moment from 'moment';
import { ImportKapiProject, ImportKapiProjectRef } from '../ImportKapiProjectModal/ImportKapiProjectModal';
import { RepoMenuHeaderCard } from '@/RepoMenuHeaderCard';

let timer: any = null;
let userSelectedGroup: boolean = false;

// 字符串转换为0-n的数字
export function stringToNumber(str: string | number, n: number) {
    if (typeof str === 'string') {
        let sum = 0;
        for (let i = 0; i < str.length; i++) {
            sum += str.charCodeAt(i);
        }
        return sum % (n + 1);
    } else {
        return str % (n + 1);
    }
}

interface ProjectItemProps {
    name: string;
    description?: string;
    id: number;
    refresh?: () => void;
    pinned: boolean;
    onEdit?: (projectDetail: any) => void;
    updateTime?: number;
    admin?: any[];
}
// 项目item
function ProjectItem({ name, description, id, refresh, pinned, onEdit, updateTime, admin = [] }: ProjectItemProps) {
    const handleEdit = (e) => {
        e.stopPropagation();
        // 获取项目详情
        nsMockManageApiManageMainApiProgram.remote({ id }).then(res => {
            onEdit?.(res);
        });
    };

    const handleDelete = (e: React.MouseEvent<HTMLSpanElement>) => {
        e.stopPropagation();
        Modal.confirm({
            title: '删除项目',
            content: `确定要删除项目"${name}"吗？`,
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
                nsMockManageApiManageMainApiProgram.remove({ id }).then(() => {
                    message.success('删除项目成功');
                    refresh?.();
                    if (pinned) {
                        document.dispatchEvent(new CustomEvent('getPinList'));
                    }
                });
            }
        });
    };

    const handleClick = () => {
        pushKey({
            projectId: id
        });
        router.push(ERouter.API_MOCK_REPO_MGR_PROJECT_INFO, { projectId: id, viewType: 'projectView' });
    }

    return (
        <div className={classNames(css.projectItem)} onClick={handleClick}>
            <div className={css.line1}>
                <div className={classNames(css.projectIcon, css[`color${stringToNumber(id, 5)}`])}>
                    <KdevIconFont id={common_system_api} style={{ fontSize: 21 }} />
                </div>
                <Tooltip title={name}>
                    <div className={css.projectName}>
                        {name}
                    </div>
                </Tooltip>
                <div className={css.options}>
                    {
                        pinned ?
                            <Tooltip title="移除导航栏">
                                <span onClick={(e) => {
                                    e.stopPropagation();
                                    nsMockManageApiManageMainApiProgramUnpin.remote({ id }).then(res => {
                                        message.success('已移除导航栏');
                                        refresh?.();
                                        document.dispatchEvent(new CustomEvent('getPinList'));
                                    });
                                }}>
                                    <KdevIconFont
                                        className={classNames(css.optionIcon, css.mian)}
                                        id={common_system_pinmian}
                                    />
                                </span>
                            </Tooltip>
                            : <Tooltip title="添加至导航栏">
                                <span onClick={(e) => {
                                    e.stopPropagation();
                                    nsMockManageApiManageMainApiProgramPin.pinProject({ id }).then(res => {
                                        message.success('已添加至导航栏');
                                        refresh?.();
                                        document.dispatchEvent(new CustomEvent('getPinList'));
                                    });
                                }}>
                                    <KdevIconFont className={css.optionIcon} id={common_system_pinxian} />
                                </span>
                            </Tooltip>
                    }
                    {/* <span onClick={handleEdit}>
                        <KdevIconFont className={css.optionIcon} id={common_system_edit} />
                    </span> */}
                    <Tooltip title="项目设置">
                        <span onClick={(e) => {
                            e.stopPropagation();
                            router.push(ERouter.API_MOCK_REPO_MGR_PROJECT_SETTING, { projectId: id });
                        }}>
                            <KdevIconFont className={css.optionIcon} id={common_system_setting} />
                        </span>
                    </Tooltip>
                    <Tooltip title="删除">
                        <span onClick={handleDelete}>
                            <KdevIconFont className={css.optionIcon} id={common_system_delete} />
                        </span>
                    </Tooltip>
                </div>
            </div>
            <Tooltip title={description}>
                <div className={css.line2}>
                    {description || '暂无项目描述'}
                </div>
            </Tooltip>
            <div className={css.line3}>
                <div className={css.time}>
                    <KdevIconFont id={common_system_time} style={{ marginRight: 3, color: '#898A8C' }} />
                    {moment(updateTime).format('YYYY-MM-DD')}
                </div>
                <div className={css.user}>
                    <AvatarList
                        reviewers={(admin || []).map(item => ({
                            ...item,
                            avatarUrl: item.photo,
                        }))}
                        max={3}
                        kim
                        size={20}
                    />
                </div>
            </div>
        </div>
    );
}

function ProjectViewListHeader({ onCreateProject, currentGroupId, userGroup }) {
    const importKapiRef = useRef<ImportKapiProjectRef>(null);
    return <div className={css.header}>
        <div className={css.left}>
            <Tooltip title="切换至仓库视图">
                <div className={css.title}>
                    <h1>项目管理</h1>
                </div>
            </Tooltip>
            <RepoMenuHeaderCard
                expand={false}
                imageUrl={''}
                name={userGroup?.groupName}
                toggleIcon={false}
            />
        </div>
        <div className={css.right}>
            <Button
                type="primary"
                className={css.createProjectBtn}
                onClick={onCreateProject}
            >
                <KdevIconFont id={common_system_add} style={{ marginRight: 4 }} />
                新建项目
            </Button>
            <Button type="primary" ghost className={css.importProjectBtn} onClick={() => {
                importKapiRef.current?.open();
            }}>
                <KdevIconFont id={common_system_import} style={{ marginRight: 4 }} />
                导入 KAPI 项目
            </Button>
            <ImportKapiProject
                ref={importKapiRef}
                currentGroupId={currentGroupId}
            />
        </div>
    </div>
}

function ProjectViewListSearch(props: any) {
    return <div className={css.search}>
        <div className={css.segmented}>
            <Segmented
                options={[
                    { label: '全部项目', value: 'all' },
                    { label: '常用项目', value: 'withMe' },
                ]}
                value={props.value}
                onChange={(value) => {
                    props.onChange?.(value);
                    userSelectedGroup = true
                }}
            />
        </div>
        <div className={css.right}>
            <Input
                placeholder="支持项目名称搜索"
                style={{ width: 300 }}
                value={props.searchValue}
                onChange={(e) => {
                    props.onSearch?.(e.target.value);
                }}
                prefix={
                    <KdevIconFont
                        id={common_system_search}
                        style={{ color: '#D5D6D9', fontSize: 16 }}
                    />
                }
            />
        </div>
    </div>
}

export async function getGroup() {
    const userLastGroup = JSON.parse(localStorage.getItem(KDEV_API_GROUP) || '{}');
    if (userLastGroup.groupId) {
        return userLastGroup;
    } else {
        const res = await nsMockManageApiManageMainGroupUserDepartmentInfo.remote({});
        return res.secondDepartmentGroup;
    }
}
export function ProjectViewList() {
    // 创建项目弹框
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    // 创建项目loading
    const [createProjectLoading, setCreateProjectLoading] = useState(false);
    // 创建项目ref
    const createProjectRef = useRef<CreateProjectRef>(null);
    // 当前分组ID
    const [currentGroupId, setCurrentGroupId] = useState<number>(0);
    // 当前用户组信息
    const [userGroup, setUserGroup] = useState<any>(null);
    // 全部项目列表
    const [allProjectList, setAllProjectList] = useState<nsMockManageApiManageMainApiProgramList.ResponseItem[]>([]);
    // 常用项目列表
    const [withMeAllProjectList, setWithMeAllProjectList] = useState<
        nsMockManageApiManageMainApiProgramList.ResponseItem[]
    >([]);

    const [withMeFilterProjectList, setWithMeFilterProjectList] = useState<
        nsMockManageApiManageMainApiProgramList.ResponseItem[]
    >([]);

    // 分页相关状态
    const [pageNumber, setPageNumber] = useState<number>(1);
    const [hasMore, setHasMore] = useState<boolean>(true);
    // 全部项目、常用项目
    const [projectType, setProjectType] = useState<'all' | 'withMe'>('withMe');
    // 是否是编辑模式
    const [isEditMode, setIsEditMode] = useState(false);
    // 当前编辑的项目ID
    const [currentEditId, setCurrentEditId] = useState<number>(0);
    const [searchValue, setSearchValue] = useState('');

    const getProjectList = useCallback(async (loadMore?: boolean) => {
        if (projectType === 'withMe') {
            if (!currentGroupId) { return }
            nsMockManageApiManageMainApiProgramHot.remote({
                groupId: currentGroupId
            }).then((res) => {
                setWithMeAllProjectList(res);
                setWithMeFilterProjectList(res.filter(item => item.name.includes(searchValue)));
                // 常用项目列表不需要分页，一次性获取全部
                setHasMore(false);
                // 如果常用项目为空，则切换到全部项目
                if (res.length === 0 && !userSelectedGroup) {
                    setProjectType('all');
                }
            })
        } else {
            // 如果是加载更多且没有更多数据，则直接返回
            if (loadMore && !hasMore) {
                return;
            }
            // 如果不是加载更多，重置页码
            if (!loadMore) {
                setPageNumber(1);
            }
            const currentPage = loadMore ? pageNumber + 1 : 1;
            if (!currentGroupId) { return }
            if (searchValue) {
                clearTimeout(timer);
                timer = setTimeout(() => {
                    nsMockManageApiManageMainApiProgramSearch.remote({
                        pageNumber: currentPage,
                        pageSize: 100,
                        filters: [
                            {
                                type: 'term',
                                field: 'groupId',
                                value: +currentGroupId
                            },
                            {
                                type: 'fuzzy',
                                field: 'name',
                                value: searchValue
                            }
                        ]
                    }).then((res) => {
                        const newItems = res.items || [];
                        // 判断是否还有更多数据
                        const total = res.total || 0;
                        const hasMoreData = currentPage * 100 < total;

                        // 更新状态
                        setHasMore(hasMoreData);
                        setPageNumber(currentPage);

                        // 如果是加载更多，则追加数据，否则替换数据
                        if (loadMore) {
                            setAllProjectList(prevList => [...prevList, ...newItems]);
                        } else {
                            setAllProjectList(newItems);
                        }
                    });
                }, 500);
            } else {
                clearTimeout(timer);
                nsMockManageApiManageMainApiProgramList.remote({
                    pageNumber: currentPage,
                    pageSize: 100,
                    groupId: currentGroupId
                }).then((res) => {
                    const newItems = res.items || [];
                    // 判断是否还有更多数据
                    const total = res.total || 0;
                    const hasMoreData = currentPage * 100 < total;

                    // 更新状态
                    setHasMore(hasMoreData);
                    setPageNumber(currentPage);

                    // 如果是加载更多，则追加数据，否则替换数据
                    if (loadMore) {
                        setAllProjectList(prevList => [...prevList, ...newItems]);
                    } else {
                        setAllProjectList(newItems);
                    }
                })
            }
        }
    }, [currentGroupId, projectType, searchValue, pageNumber, hasMore])
    // 获取项目列表
    // useEffect(() => {
    //     getProjectList();
    // }, [currentGroupId]);
    // 获取项目列表
    useEffect(() => {
        getProjectList();
    }, [projectType, currentGroupId]);

    // 搜索
    useEffect(() => {
        if (projectType === 'all') {
            getProjectList();
        } else {
            setWithMeFilterProjectList(withMeAllProjectList.filter(item => item.name.includes(searchValue)));
        }
    }, [searchValue]);

    // 处理滚动加载更多数据
    const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
        const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
        // 当滚动到距离底部20px时，加载更多数据
        if (scrollHeight - scrollTop - clientHeight < 20 && hasMore) {
            getProjectList(true);
        }
    };

    // 获取分组ID并赋值
    const changeGroup = async () => {
        const group = await getGroup();
        setCurrentGroupId(group.groupId);
        setUserGroup(group);
    }

    // 监听分组改变
    useEffect(() => {
        document.addEventListener('selectedGroup', changeGroup);
        // 使用自定义事件处理器来包装 getProjectList
        const handleGetProjectList = () => {
            getProjectList();
        };
        document.addEventListener('getProjectList', handleGetProjectList);
        changeGroup();

        return () => {
            document.removeEventListener('selectedGroup', changeGroup);
            document.removeEventListener('getProjectList', handleGetProjectList);
        }
    }, [currentGroupId, getProjectList]);

    const handleCreateProject = () => {
        setIsEditMode(false);
        setIsCreateModalOpen(true);
    };

    const handleEditProject = (projectDetail: any) => {
        setIsEditMode(true);
        setCurrentEditId(projectDetail.id);
        setIsCreateModalOpen(true);
        // 回填表单数据
        requestAnimationFrame(() => {
            createProjectRef.current?.setFormData({
                name: projectDetail.name,
                desc: projectDetail.desc,
                bindProjects: projectDetail.bindProjects || []
            });
        })
    };

    const handleCreateModalCancel = () => {
        setIsCreateModalOpen(false);
        setIsEditMode(false);
        setCurrentEditId(0);
        createProjectRef.current?.reset();
    };

    const handleCreateModalOk = () => {
        if (!createProjectRef.current?.check()) {
            return;
        }

        const params = createProjectRef.current.getParams();
        params.bindProjects = params.bindProjects.map(item => ({
            projectId: item.projectId
        }));
        setCreateProjectLoading(true);

        if (isEditMode) {
            // 编辑项目
            nsMockManageApiManageMainApiProgram.update({
                ...params,
                id: currentEditId,
                groupId: currentGroupId
            }).then((res) => {
                message.success('编辑项目成功');
                setCreateProjectLoading(false);
                setIsCreateModalOpen(false);
                setIsEditMode(false);
                setCurrentEditId(0);
                createProjectRef.current?.reset();
                getProjectList();
                const project = allProjectList.find(i => i.id === currentEditId);
                if (project?.pinned) {
                    document.dispatchEvent(new CustomEvent('getPinList'));
                }
            }).catch(err => {
                setCreateProjectLoading(false);
            });
        } else {
            if (params.name.length > 60) {
                message.error('项目名称过长');
                setCreateProjectLoading(false);
                return;
            }
            if (params.desc.length > 150) {
                message.error('项目描述过长');
                setCreateProjectLoading(false);
                return;
            }
            // 新建项目
            nsMockManageApiManageMainApiProgram.create({
                ...params,
                groupId: currentGroupId
            }).then(() => {
                message.success('创建项目成功');
                setCreateProjectLoading(false);
                setIsCreateModalOpen(false);
                createProjectRef.current?.reset();
                getProjectList();
            }).catch(err => {
                setCreateProjectLoading(false);
            });
        }
    };


    return (
        <div className={css.projectViewList}>
            <ProjectViewListHeader onCreateProject={handleCreateProject} currentGroupId={currentGroupId} userGroup={userGroup} />
            <ProjectViewListSearch
                value={projectType}
                onChange={(value) => {
                    setProjectType(value);
                }}
                searchValue={searchValue}
                onSearch={(value) => {
                    setSearchValue(value);
                }}
            />
            <div className={css.scroll} onScroll={handleScroll}>
                <div className={css.allProjectList}>
                    {(projectType === 'all' ? allProjectList : withMeFilterProjectList).length === 0 ? (
                        <KEmpty className={css.empty} image="NOMAL_EMPTY_LIST" description="暂无数据" />
                    ) : null}
                    {(projectType === 'all' ? allProjectList : withMeFilterProjectList).map((item) => (
                        <ProjectItem
                            key={item.id}
                            name={item.name}
                            description={item.desc}
                            id={item.id}
                            refresh={() => {
                                getProjectList();
                            }}
                            pinned={item.pinned}
                            onEdit={handleEditProject}
                            updateTime={item.updateTime}
                            admin={item.admins} // 由于 ResponseItem 类型中没有 admins 属性，这里传入空数组
                        />
                    ))}
                </div>
            </div>

            {/* 创建/编辑项目弹框 */}
            <Modal
                title={isEditMode ? '编辑项目' : '新建项目'}
                open={isCreateModalOpen}
                onCancel={handleCreateModalCancel}
                onOk={handleCreateModalOk}
                okButtonProps={{ loading: createProjectLoading }}
                width="480px"
            >
                <CreateProject ref={createProjectRef} />
            </Modal>
        </div>
    )
}
