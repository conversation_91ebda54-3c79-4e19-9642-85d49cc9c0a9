.container {
    padding: 16px 20px;
}

h2 {
    color: var(---text_primary, #252626);
    /* font_title_4 */
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
}

.content {
    padding: 0;
    border-radius: 4px;
}

.formItem {
    margin-bottom: 24px;
    display: flex;
    align-items: flex-start;
}

.labelWrapper {
    width: 100px;
    display: flex;
    align-items: center;
    padding-right: 8px;
    padding-top: 5px;
}

.label {
    font-size: 14px;
    color: #252626;

    &.required {
        &::after {
            content: '*';
            color: #ff4d4f;
            margin-left: 4px;
        }
    }
}

.inputWrapper {
    flex: 1;
}

.projectNameInput {
    width: 528px;
}

.projectDescInput {
    width: 528px;
}

.tip {
    margin-left: 8px;
    cursor: pointer;
    color: #898A8C;
    display: inline-flex;
    align-items: center;
}

.repoSelectContainer {
    margin-top: 8px;
}

.buttonContainer {
    margin-top: 32px;
}