@import '~@lynx/design-token/dist/less/token.less';

.projectInfoLeft {
  padding: 12px 16px 0;
  height: 100%;
  display: flex;
  flex-direction: column;

}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  width: 100%;
  overflow: hidden;
  min-width: 0;
  /* 确保 flex 容器可以收缩 */

  .title {
    color: var(---text_tertiary, #898A8C);
    /* font_body */
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    cursor: pointer;
    white-space: nowrap;
  }

  .divider {
    margin: 0 8px;
    color: #D5D6D9ff;
  }

  .projectName {
    color: var(---text_primary, #252626);
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0.16px;
    flex: 1;
    min-width: 0;
    /* 这是确保 flex 项目可以收缩到比内容更小的关键 */
  }
}

.search {
  display: flex;
  margin-bottom: 12px;
}

.projectList {
  flex: 1 0 0;
  overflow: hidden;

  .projectTree {
    height: 100%;
  }

  .projectTitle {
    display: flex;
    align-items: center;
    color: #252626ff;
    font-size: 14px;
    height: 36px;
    padding: 7px 8px;

    span {
      margin-left: 8px;
      flex: 1;
    }

    .addIcon {
      display: none;
      cursor: pointer;
    }

    &:hover {
      .addIcon {
        display: block;
      }
    }

  }


}

.repoDirectoryTree {
  padding-left: 0;

  :global {

    .ant-tree-treenode {
      padding: 0;
    }

    .ant-tree-list-scrollbar {
      width: 6px !important;
      right: -6px !important;
    }

    .ant-tree-list-scrollbar-thumb {
      background-color: @color-bg-tertiary !important;
      border-radius: 6px !important;
    }

    .ant-tree-treenode {
      height: 38px;
      display: flex;
      align-items: center;
      border-radius: 4px;
      color: @color-text-primary;
    }

    .ant-tree-treenode::before {
      bottom: 0;
      border-radius: 4px;
      height: 38px;
    }

    .ant-tree-treenode:hover::before {
      background-color: @color-bg-basic-hover;
    }

    .ant-tree-node-content-wrapper.ant-tree-node-selected {
      color: @color-text-brand !important;
    }

    .ant-tree-title {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }

    .ant-tree-node-content-wrapper {
      overflow: hidden;
      padding: 0 0 0 4px;
      // height: 100%;
      height: 36px;
    }

    .ant-tree-switcher {
      width: 16px !important;
      height: 32px;
      // line-height: 22px;
      display: flex;
      align-items: center !important;
      align-self: center !important;
      padding-left: 4px;
      margin-top: -1px;
    }

    .ant-tree-switcher .ant-tree-switcher-icon {
      font-size: 14px;
      color: @color-text-icon-secondary;
    }

    .ant-tree-indent-unit {
      width: 16px;
    }

    .ant-tree-treenode-selected {
      &::before {
        background-color: #F0F4FF !important;
      }

      .svg-common_system_folder_close_line,
      .svg-common_system_auto_import_folder {
        color: @color-bg-brand !important;
      }
    }
  }



}

.searchTreeResult {
  padding: 0px !important;
  width: 360px !important;
  min-width: 360px !important;
}