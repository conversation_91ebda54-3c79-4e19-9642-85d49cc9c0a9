import React, { useCallback, useEffect, useState } from 'react'
import css from './QuickAccess.less';
import { stringToNumber } from '../ProjectViewList/ProjectViewList';
import { common_system_api, common_system_closeplus } from '@kid/enterprise-icon/icon/output/icons';
import { KdevIconFont } from '@/business/commonComponents';
import classNames from 'classnames';
import { message } from 'antd';
import { nsMockManageApiManageMainApiProgramPin, nsMockManageApiManageMainApiProgramUnpin } from '@/remote';
import { router } from '@libs/mvvm'
import { ERouter } from 'CONFIG';
import { useLocation } from 'react-router-dom';

export function QuickAccess() {
    const [list, setList] = useState<any[]>([]);
    const [activedId, setActivedId] = useState<number>();

    const location = useLocation();
    useEffect(() => {
        const url = new URLSearchParams(location.search);
        setActivedId(Number(url.get('projectId')) || 0);
    }, [location]);

    const getPinList = useCallback(() => {
        nsMockManageApiManageMainApiProgramPin.getPinList().then(res => {
            setList(res);
        })
    }, []);
    useEffect(() => {
        getPinList();
        setTimeout(() => {
            document.addEventListener('getPinList', getPinList);
        }, 1000)
        return () => {
            document.removeEventListener('getPinList', getPinList);
        }
    }, []);
    return (
        <div className={css.additional}>
            {
                list.length > 0 ? <>
                    <div className={css.header}>
                        <span>快捷访问</span>
                        <div className={css.line}></div>
                    </div>

                    <div className={css.list}>
                        {
                            list.map(item => <div
                                className={classNames(css.item, { [css.actived]: activedId === item.id })}
                                key={item.id}
                                onClick={() => {
                                    setActivedId(item.id);
                                    router.push(ERouter.API_MOCK_REPO_MGR_PROJECT_INFO, {
                                        projectId: item.id,
                                        viewType: 'projectView'
                                    });
                                    document.dispatchEvent(new CustomEvent('projectIdChange', {
                                        detail: {
                                            projectId: item.id
                                        }
                                    }));
                                }}
                            >
                                <div className={classNames(css.projectIcon, css[`color${stringToNumber(item.id, 5)}`])}>
                                    <KdevIconFont id={common_system_api} style={{ fontSize: 12 }} />
                                </div>
                                <span>{item.name}</span>
                                <div className={css.close} onClick={() => {
                                    nsMockManageApiManageMainApiProgramUnpin.remote({ id: item.id }).then(() => {
                                        message.success('已移除快捷访问');
                                        getPinList();
                                        document.dispatchEvent(new CustomEvent('getProjectList'));
                                    });
                                }}>
                                    <KdevIconFont
                                        id={common_system_closeplus}
                                        style={{ fontSize: 12, color: '#898A8C' }}
                                    />
                                </div>
                            </div>)
                        }
                    </div>
                </>
                    : null
            }
        </div>
    )
}
