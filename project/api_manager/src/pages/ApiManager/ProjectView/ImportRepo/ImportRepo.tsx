import React, { useState, useImperativeHandle, forwardRef, Ref, useEffect, useContext, useCallback } from 'react';
import { Checkbox, message, Tree, Input, Modal, Spin, Button, Alert } from 'antd';
import css from './ImportRepo.less';
import classnames from 'classnames';
import { common_system_search, common_system_folder_close_line, common_system_folder_expand1, common_system_folder_addition } from '@kid/enterprise-icon/icon/output/icons';
import { KdevIconFont } from '@/business/commonComponents';
import { nsMockManageApiManageMainApiProgramImportRepoApi, nsMockManageApiManageMainApiProgramImportRepoGroupTree as TreeApi } from '@/remote';
import { ApiManageProjectInfoContext, ApiManageProjectInfoContextType } from '../ProjectInfo/ProjectInfo';
import { DirectorySelect } from '@/pages/ApiManager/ProjectView/DirectorySelect/DirectorySelect';
import { DomainSelect } from '@/pages/ApiManager/ProjectView/DomainSelect/DomainSelect';

/**
 * 导入仓库组件的引用接口，用于父组件调用内部方法
 */
export interface ImportRepoRef {
    getParams(): any;
    reset(): void;
    check(): boolean;
    setFormData(data: {
        repoId: number | null;
        checkedKeys?: React.Key[];
    }): void;
    open(): void;
    close(): void;
}

/**
 * 导入仓库组件属性接口
 */
interface ImportRepoProps {
    onSuccess?: () => void;
}

/**
 * 树节点标题渲染组件
 * 为不同类型的节点显示不同图标
 */
export const RenderTitle = (props: { title: string, node: TreeApi.IReturn, expanded: boolean, filterText: string }) => {
    const { title, node, expanded, filterText } = props;
    const reg = new RegExp(`(${filterText})`, 'gi');
    return <div className={css.titleContainer}>
        {
            (node.type === 'GROUP' || node.type === 'CATALOG') && (
                <KdevIconFont style={{ color: '#898A8Cff', marginRight: 4 }}
                    id={expanded ? common_system_folder_expand1 : common_system_folder_close_line} />
            )
        }
        <span className={css.titleText} dangerouslySetInnerHTML={{ __html: title.replace(reg, '<span style="color: #326BFB;">$1</span>') }} />
    </div>;
};

/**
 * 导入仓库组件
 * 用于选择API并配置导入选项
 */
export const ImportRepo = forwardRef((props: ImportRepoProps, ref: Ref<ImportRepoRef>) => {
    const {
        currentGroupId,
        currentFirstGroupId,
        projectId,
        openCreateCatalogModal
    } = useContext(ApiManageProjectInfoContext) as ApiManageProjectInfoContextType;
    const [importRepoGroupId, setImportRepoGroupId] = useState<number>(currentFirstGroupId);
    const { onSuccess } = props;
    const [isOpen, setIsOpen] = useState(false);
    const [importLoading, setImportLoading] = useState(false);
    const [treeLoading, setTreeLoading] = useState(false);

    // 表单数据
    const [repoId, setRepoId] = useState<number | undefined>(undefined);
    const [searchValue, setSearchValue] = useState('');

    // Tree相关状态
    const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
    const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
    const [searchCheckedKeys, setSearchCheckedKeys] = useState<React.Key[]>([]);
    const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
    const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
    const [totalApiCount, setTotalApiCount] = useState<number>(0);
    const [apiNodeMap, setApiNodeMap] = useState<Record<string | number, boolean>>({});
    const [selectedApiCount, setSelectedApiCount] = useState<number>(0);

    // 迁移策略选项
    const [syncTestHistory, setSyncTestHistory] = useState(true);
    const [syncMockData, setSyncMockData] = useState(true);
    const [treeData, setTreeData] = useState<TreeApi.IReturn[]>([]);

    useEffect(() => {
        setImportRepoGroupId(currentFirstGroupId);
    }, [currentFirstGroupId]);

    /**
     * 获取所有可展开节点的ID
     * 用于初始化树的展开状态
     */
    const getAllExpandedKeys = useCallback((arr: TreeApi.IReturn[]) => {
        const res: number[] = [];
        const loop = (nodes: TreeApi.IReturn[]) => {
            nodes.forEach(item => {
                res.push(item.id);
                if (item.children) {
                    loop(item.children);
                }
            });
        };
        loop(arr);
        return res;
    }, []);

    /**
     * 计算API节点的总数并记录所有API节点ID
     * 用于统计API节点数量和后续API节点选择判断
     */
    const countApiNodes = useCallback((nodes: TreeApi.IReturn[]): number => {
        let count = 0;
        const apiMap: Record<string | number, boolean> = {};

        const traverse = (items: TreeApi.IReturn[]) => {
            items.forEach(item => {
                if (item.type === 'API') {
                    count++;
                    apiMap[item.id] = true;
                }
                if (item.children && item.children.length > 0) {
                    traverse(item.children);
                }
            });
        };

        traverse(nodes);
        setApiNodeMap(apiMap);
        return count;
    }, []);

    /**
     * 计算已选择的API节点数量
     * 通过检查节点ID是否在API节点映射表中来确定
     */
    const countSelectedApiNodes = useCallback((keys: React.Key[]) => {
        let count = 0;
        keys.forEach(key => {
            if (apiNodeMap[key]) {
                count++;
            }
        });
        return count;
    }, [apiNodeMap]);



    /**
     * 监听选中节点变化，更新已选择的API节点数量
     */
    useEffect(() => {
        const apiCount = countSelectedApiNodes(checkedKeys);
        setSelectedApiCount(apiCount);
    }, [checkedKeys, countSelectedApiNodes]);

    function formatTreeData(data: TreeApi.IReturn[]) {
        return data.map(item => {
            if (item.type === 'GROUP') {
                return {
                    ...item,
                    id: 'GROUP_' + item.id,
                    children: formatTreeData(item.children)
                }
            } else if (item.type === 'API') {
                return {
                    ...item,
                }
            }
            return item;
        })
    }
    /**
     * 对话框打开时加载树数据
     */
    useEffect(() => {
        if (isOpen) {
            setTreeLoading(true);
            TreeApi.remote({
                groupId: importRepoGroupId.toString()
            }).then(res => {
                const data = formatTreeData([res]);
                setTreeData(data);
                setExpandedKeys(getAllExpandedKeys(data));
                const apiCount = countApiNodes(data);
                setTotalApiCount(apiCount);
            }).finally(() => {
                setTreeLoading(false);
            });
        }
    }, [isOpen, countApiNodes, getAllExpandedKeys, importRepoGroupId]);



    /**
     * 处理节点展开事件
     */
    const onExpand = (expandedKeysValue: React.Key[]) => {
        setExpandedKeys(expandedKeysValue);
        setAutoExpandParent(false);
    };

    /**
     * 处理节点选择事件
     * 区分搜索状态和非搜索状态
     */
    const onCheck = (checkedKeysValue: any, info: any) => {
        const key = info.node.key;
        if (searchValue) {
            // 搜索状态下，只同步当前操作的节点到 searchCheckedKeys
            let newSearchCheckedKeys = [...searchCheckedKeys];
            if (info.checked) {
                if (!newSearchCheckedKeys.includes(key)) {
                    newSearchCheckedKeys.push(key);
                    setCheckedKeys(newSearchCheckedKeys)
                }
            } else {
                newSearchCheckedKeys = newSearchCheckedKeys.filter(k => k !== key);
            }
            setSearchCheckedKeys(newSearchCheckedKeys);
        } else {
            // 非搜索状态，正常同步到 checkedKeys
            const newCheckedKeys = Array.isArray(checkedKeysValue)
                ? checkedKeysValue
                : checkedKeysValue.checked;
            setCheckedKeys(newCheckedKeys);
        }
    };

    /**
     * 处理节点选中事件
     */
    const onSelect = (selectedKeysValue: React.Key[]) => {
        setSelectedKeys(selectedKeysValue);
    };

    /**
     * 递归过滤树数据，返回包含匹配节点及其父节点的结构
     * 用于搜索功能
     */
    const getSearchTreeData = (data: TreeApi.IReturn[], search: string): TreeApi.IReturn[] => {
        if (!search) return data;
        const match = (title: string, apiPath: string) =>
            title.toLowerCase().includes(search.toLowerCase()) || apiPath.toLowerCase().includes(search.toLowerCase());
        const loop = (nodes: TreeApi.IReturn[]):
            TreeApi.IReturn[] => {
            return nodes
                .map(node => {
                    if (match(String(node.name), String(node.apiPath))) {
                        return { ...node };
                    }
                    if (node.children) {
                        const children = loop(node.children);
                        if (children.length) {
                            return { ...node, children };
                        }
                    }
                    return null;
                })
                .filter(Boolean) as TreeApi.IReturn[];
        };
        return loop(data);
    };

    // 获取搜索过滤后的树数据
    const searchTreeData = getSearchTreeData(treeData, searchValue);

    /**
     * 搜索值改变时，同步搜索状态下的选中节点
     */
    useEffect(() => {
        setSearchCheckedKeys(checkedKeys)
    }, [searchValue]);

    /**
     * 重置表单数据
     */
    const reset = () => {
        setRepoId(undefined);
        setCheckedKeys([]);
        setSearchCheckedKeys([]);
        setSyncTestHistory(false);
        setSyncMockData(false);
        setSearchValue('');
        setImportRepoGroupId(currentFirstGroupId);
    };

    /**
     * 表单校验
     * 检查必填项是否已填写
     */
    const check = () => {
        if (!repoId) {
            message.error('请选择目标目录');
            return false;
        }
        if (checkedKeys.length === 0) {
            message.error('请至少选择一个接口');
            return false;
        }
        return true;
    };

    /**
     * 获取表单参数
     * 用于提交表单数据
     */
    const getParams = () => ({
        repoId,
        apiIds: checkedKeys,
        syncTestHistory,
        syncMockData
    });

    /**
     * 设置表单数据
     * 用于外部组件填充表单
     */
    const setFormData = (data: {
        repoId: number | null;
        checkedKeys?: React.Key[];
    }) => {
        setRepoId(data.repoId ? Number(data.repoId) : undefined);
        if (data.checkedKeys) {
            setCheckedKeys(data.checkedKeys);
            setSearchCheckedKeys(data.checkedKeys);
        }
    };

    /**
     * 处理目录选择变化
     */
    const handleDirectoryChange = (value: number | undefined) => {
        setRepoId(value);
    };

    /**
     * 处理确认按钮点击
     * 校验表单并提交数据
     */
    const handleModalOk = async () => {
        if (!check()) {
            return;
        }

        setImportLoading(true);
        try {
            nsMockManageApiManageMainApiProgramImportRepoApi.remote({
                apiIds: checkedKeys.filter(key => Number(key)).map(key => Number(key)),
                targetProgram: projectId,
                targetCatalog: repoId!,
                syncTestHistory,
                syncMockData
            });

            // 成功后重置表单并关闭弹窗
            reset();
            setIsOpen(false);
            onSuccess?.();
            message.success('开始导入，完成后通过KIM通知');
        } catch (error) {
            message.error('导入失败');
        } finally {
            setImportLoading(false);
        }
    };

    /**
     * 处理取消按钮点击
     * 关闭对话框并重置表单
     */
    const handleModalCancel = () => {
        setIsOpen(false);
        reset();
    };

    /**
     * 暴露给父组件的方法
     */
    useImperativeHandle(ref, () => ({
        getParams,
        reset,
        check,
        setFormData,
        open: () => setIsOpen(true),
        close: () => setIsOpen(false)
    }));

    return (
        <Modal
            title={<span className={css.title}>
                导入仓库视图接口
                <span className={css.subTitle}>（导入并新建接口）</span>
            </span>}
            open={isOpen}
            onCancel={handleModalCancel}
            onOk={handleModalOk}
            okButtonProps={{ loading: importLoading }}
            width="640px"
        >
            <div>
                <Alert message="重要提示：迁移成功后，原仓库接口会复制一份新数据到当前项目中，两份数据完全独立，后续不会双向同步。"
                    type="warning" showIcon closable style={{ marginBottom: 24, color: '#252626' }} />

                <div className={css.importRepoContentItem}>
                    <span
                        className={classnames(css.required, css.importRepoContentItemTitle)}>
                        领域选择
                    </span>
                    <DomainSelect
                        value={importRepoGroupId}
                        onChange={value => setImportRepoGroupId(Number(value || 0))}
                        style={{ width: '100%' }}
                    />
                </div>
                <div className={css.importRepoContentItem}>
                    <span
                        className={classnames(css.required, css.importRepoContentItemTitle)}>
                        原接口选择（{selectedApiCount}/{totalApiCount}）
                    </span>
                    <div className={css.importRepoContentItemTree}>
                        <Input
                            placeholder="请输入关键词进行搜索"
                            value={searchValue}
                            onChange={e => setSearchValue(e.target.value)}
                            className={css.searchInput}
                            prefix={<KdevIconFont id={common_system_search} style={{ color: '#D5D6D9ff' }} />}
                        />
                        <div className={css.importRepoContentItemTreeContent}>
                            <Spin spinning={treeLoading} tip="数据加载中...">
                                <Tree
                                    className={css.importRepoContentItemTreeContentTree}
                                    checkable
                                    onExpand={onExpand}
                                    expandedKeys={expandedKeys}
                                    onCheck={onCheck}
                                    checkedKeys={searchValue ? searchCheckedKeys : checkedKeys}
                                    onSelect={onSelect}
                                    selectedKeys={selectedKeys}
                                    treeData={(searchValue ? searchTreeData : treeData) as any}
                                    height={237}
                                    virtual={true}
                                    defaultExpandAll
                                    fieldNames={{
                                        title: 'name',
                                        key: 'id',
                                        children: 'children'
                                    }}
                                    titleRender={(node: any) => {
                                        const typedNode = node as TreeApi.IReturn;
                                        return <RenderTitle
                                            title={typedNode.name}
                                            node={typedNode}
                                            expanded={expandedKeys.includes(typedNode.id)}
                                            filterText={searchValue}
                                        />;
                                    }}
                                />
                            </Spin>
                        </div>
                    </div>
                </div>
                <div className={css.importRepoContentItem}>
                    <span
                        className={classnames(css.required, css.importRepoContentItemTitle)}>迁移策略</span>
                    <div>
                        <Checkbox
                            checked={syncTestHistory}
                            onChange={e => setSyncTestHistory(e.target.checked)}
                        >
                            保留原接口测试数据
                        </Checkbox>
                        <Checkbox
                            checked={syncMockData}
                            onChange={e => setSyncMockData(e.target.checked)}
                        >
                            保留原接口 Mock 数据
                        </Checkbox>
                    </div>
                </div>
                <div className={css.importRepoContentItem}>
                    <span
                        className={classnames(css.required, css.importRepoContentItemTitle)}>目标目录</span>
                    <DirectorySelect
                        placeholder="请选择目录"
                        value={repoId}
                        programId={projectId}
                        onChange={handleDirectoryChange}
                        style={{ width: '100%' }}
                        dropdownFooter={
                            <div style={{ padding: '8px', textAlign: 'center' }}>
                                <Button
                                    type="text"
                                    icon={<KdevIconFont id={common_system_folder_addition} />}
                                    onClick={() => {
                                        openCreateCatalogModal(0, '');
                                    }}
                                    style={{ width: '100%' }}
                                >
                                    新建目录
                                </Button>
                            </div>
                        }
                    />
                </div>
            </div>
        </Modal>
    );
});