.selectRepoListContainer {
    width: 100%;
    overflow: hidden;
    // border-radius: 8px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    // background-color: #fff;
    // padding: 16px;

    .addRepoButtonWarp {}

    .addRepoButton {
        padding: 0;
        display: flex;
        align-items: center;
        // justify-content: center;
        color: #327DFF;
        cursor: pointer;
        // margin-bottom: 16px;
        font-size: 14px;
    }
}

.addRepoPopoverContent {

    .searchBox {
        margin-bottom: 8px;
        padding: 0 8px;

        .searchInput {
            width: 100%;
            border-radius: 4px;
        }
    }

    .repoList {
        max-height: 252px;
        overflow-y: auto;
        gap: 8px;

        .repoItem {
            display: flex;
            align-items: center;
            height: 36px;
            border-radius: 4px;
            cursor: pointer;
            padding: 0 8px;
            width: 100%;

            &.active {
                background-color: #F5F7FA;

                .projectName {
                    color: #326BFB;
                }
            }



            &:hover {
                background-color: #F5F7FA;
            }

            .checkbox {
                margin-right: 8px;
            }

            .projectName {
                flex: 1 0 0;
                width: 0;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                color: var(---text_primary, #252626);
                font-family: "PingFang SC";
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
            }



            .copyIcon {
                margin-left: 8px;
                color: #898A8C;
                cursor: pointer;
                visibility: hidden;
            }

            &:hover .copyIcon {
                visibility: visible;
            }
        }
    }

    .bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;

        .line {
            width: 1px;
            height: 16px;
            background-color: #EBEDF0;
        }

        .addProject,
        .import {
            flex: 1 1 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            color: var(---text_primary, #575859);
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            cursor: pointer;
        }

        .line {
            width: 1px;
            height: 16px;
            background-color: #EBEDF0;
        }
    }
}

.repoShowList {
    display: flex;
    gap: 8px;
    flex-direction: column;
    margin-bottom: 8px;

    .repoShowItem {
        display: flex;
        align-items: center;
        padding: 5px 12px;
        flex: 1 0 0;
        border-radius: 4px;
        border: 1px solid var(---stroke_table, #EBEDF0);
        background: #FFF;

        .repoShowItemName {
            flex: 1 0 0;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
    }
}

.arrow {
    transition: transform 0.3s ease-in-out;
}

.open {
    transform: rotate(180deg);
}

.menuOverlay {
    // left: 12px !important;
    // padding: 8px 21px;

    :global {
        .ant-popover-arrow {
            display: none;
        }

        .ant-popover-inner-content {
            padding: 12px 8px;
            width: 432px;
        }
    }
}