import { checkObjectValueUnique } from 'libs';

// 无论 ERouter 定义为 enum 还是 object， key 值重复 依赖 compiler 检测（tslint 4.0开始  已去除 no-duplicate-key rule）
export enum ERouter {
    ROOT = '/',
    DEMO = '/web/api-mock/demo',

    /**
     * api-mgr
     */
    API_MGR_ROOT = '/web/api-mock',

    // git仓库管理
    API_MOCK_REPO = '/web/api-mock/repo',
    /**
     * 目录入口的管理api
     */
    API_MOCK_REPO_MGR = '/web/api-mock/repo/mgr',
    API_MOCK_REPO_MGR_PROJECT = '/web/api-mock/repo/project',
    API_MOCK_REPO_MGR_PROJECT_INFO = '/web/api-mock/repo/mgr/projectInfo',
    API_MOCK_REPO_MGR_PROJECT_SETTING = '/web/api-mock/repo/mgr/projectSetting',
    API_MOCK_API_VIEW = '/web/api-mock/repo/apiView',
    /**
     * 仓库入口的管理api
     */
    API_MOCK_MGR_REPO = '/web/api-mock/mgr/repo',
    // API_MOCK_REPO_API_MGR = '/web/api-mock/repo/apimgr',
    API_MOCK_REPO_EDIT_API = '/web/api-mock/repo/editapi',
    API_MOCK_REPO_API_VERSION_DIFF = '/web/api-mock/repo/apiVDiff',
    API_MOCK_REPO_API_TRACE_DIFF = '/web/api-mock/repo/traceDiff',
    API_MOCK_REPO_API_DOCS = '/web/api-mock/repo/apidocs',
    API_MOCK_REPO_API_DOCS_SHOW = '/web/api-mock/repo/showapidocs',
    API_MOCK_REPO_API_DOCS_ENTRY = '/web/api-mock/repo/entryapidocs',
    API_MOCK_REPO_CREATE_TEMPLATE = '/web/api-mock/repo/newTemplate',
    API_MOCK_REPO_MR_CHECK_API = '/web/api-mock/repo/mrCheckApi',

    // grpc 模块
    API_MOCK_GRPCMOCK = '/web/api-mock/grpcapimock',
    // API_MOCK_GRPCMOCK_MODULE = '/web/api-mock/grpcapimock/module',
    API_MOCK_GRPCMOCK_INTERFACE = '/web/api-mock/grpcapimock/interface',
    API_MOCK_GRPCMOCK_DETAIL = '/web/api-mock/grpcapimock/detail',
    // API_MOCK_GRPCMOCK_REPORT = '/web/api-mock/grpcapimock/report',

    // 组织空间
    // API_MOCK_ORGANIZESPACE = '/web/api-mock/organizespace',
    // API_MOCK_ORGANIZESPACE_PROJECTMGR = '/web/api-mock/organizespace/projectmgr', // 项目管理
    // 场景管理
    // API_MOCK_ORGANIZESPACE_SCENEMGR = '/web/api-mock/organizespace/scenemgr',
    // API_MOCK_ORGANIZESPACE_ASCENE = '/web/api-mock/organizespace/ascene',
    // 部门管理
    // API_MOCK_ORGANIZESPACE_DEPARTMENTMGR = '/web/api-mock/organizespace/departmentmgr',
    // mock代理配置
    // API_MOCK_PROXY_CONFIG = '/web/api-mock/proxyconfig',
    // 新版代理页面路由地址
    API_MOCK_PROXY = '/web/api-mock/proxy',
    API_MOCK_HTTP_PROXY = '/web/api-mock/httpProxy',
    API_MOCK_HTTP_PROXY_RULE = '/web/api-mock/httpProxy/rule',

    // HTTP
    // API_MOCK_PROJECT = '/web/api-mock/project',
    // API_MOCK_PROJECT_MODULEMGR = '/web/api-mock/project/module', // 项目下的模块管理
    // API_MOCK_PROJECT_MODULEDETAIL = '/web/api-mock/project/moduledetail', // 模块详情
    // API_MOCK_MODULEMGR_APISTATISTICS = '/web/api-mock/module/apistatistics', // 模块下API统计

    // GRPC
    // API_MOCK_PROJECT_GRPC_MODULE = '/web/api-mock/project/grpcmodule', // 项目下的grpc模块

    API_MOCK_VERSIONCOMPARISON = '/web/api-mock/versioncomparison', // http api版本对比

    // 自动解析 API
    API_MOCK_AUTOMATICPARSINGAPI = '/web/api-mock/automaticparsingapi',
    API_MOCK_HTTP_MODULE_MGR = '/web/api-mock/http/modulemgr', // HTTP 模块
    API_MOCK_HTTP_API_MGR_SWAGGER = '/web/api-mock/http/apimgr', // 不改变新版本路由，新增API_MOCK_HTTP_API_MGR
    API_MOCK_HTTP_API_MGR = '/web/api-mock/old/http/apimgr',
    API_MOCK_HTTP_API_DETAIL = '/web/api-mock/http/detail',

    // GRPC
    API_MOCK_RPC = '/web/api-mock/rpc',
    API_MOCK_RPC_API_MGR = '/web/api-mock/rpc/apimgr',
    API_MOCK_RPC_API_DETAIL = '/web/api-mock/rpc/apiDetail',

    // KDev 跳转使用不存在API管理平台
    KDEV_REPO_PIPELINE = '/web/workbench/repo/setting/pipeline',
    KDEV_PIPELINE_EDIT = '/web/workbench/newPipelineFlow',

    // Api 模板
    API_TEMPLATE = '/web/api-mock/apiTemplate',
    API_TEMPLATE_SETTING = '/web/api-mock/apiTemplateSetting'
}

const isOldHttpApiRouter = (currentPath: string) => {
    return currentPath.indexOf(ERouter.API_MOCK_HTTP_MODULE_MGR) > -1
        || currentPath.indexOf(ERouter.API_MOCK_HTTP_API_MGR) > -1
        || currentPath.indexOf(ERouter.API_MOCK_HTTP_API_DETAIL) > -1
        || currentPath.indexOf(ERouter.API_MOCK_VERSIONCOMPARISON) > -1;
};

const isOldGrpcApiRouter = (currentPath: string) => {
    return currentPath.indexOf(ERouter.API_MOCK_GRPCMOCK) > -1;
};

// 新旧版切换使用
// const isOldApiRouter = (currentPath: string) => {
//     if (isOldHttpApiRouter(currentPath) || isOldGrpcApiRouter(currentPath)) {
//         return true;
//     }
//     return (
//         // currentPath.indexOf(ERouter.API_MOCK_ORGANIZESPACE) > -1 || 
//         currentPath.indexOf(ERouter.API_MOCK_PROJECT) > -1
//         || currentPath.indexOf(ERouter.API_MOCK_MODULEMGR_APISTATISTICS) > -1
//         || currentPath.indexOf(ERouter.API_MOCK_PROXY_CONFIG) > -1
//     );
// };

const isNewApiRouter = (currentPath: string) => {
    return currentPath.indexOf(ERouter.API_MOCK_REPO) > -1
        || currentPath.indexOf(ERouter.API_MOCK_HTTP_API_MGR_SWAGGER) > -1
        || currentPath.indexOf(ERouter.API_TEMPLATE) > -1
        || currentPath.indexOf(ERouter.API_MOCK_MGR_REPO) > -1
        || currentPath.indexOf(ERouter.API_MOCK_RPC) > -1
        || currentPath === ERouter.API_MOCK_PROXY;
};

checkObjectValueUnique(ERouter, 'ERouter');

export { isNewApiRouter };// isOldApiRouter 
