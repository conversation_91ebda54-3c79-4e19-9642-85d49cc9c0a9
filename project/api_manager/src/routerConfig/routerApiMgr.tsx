import { RouteSetting } from '@libs/a-component';
import { ERouter } from 'CONFIG';
import { ApiOutlined } from '@ant-design/icons';
import React from 'react';
import { KdevIconFont } from 'kdev-fe-common/src/component/KdevIconFont';
import { new_kdev_api } from '@kid/enterprise-icon/icon/output/icons';

const routerRepoMgr: RouteSetting = new RouteSetting({
    iconType: <KdevIconFont id={new_kdev_api} className="anticon" />,
    title: 'API 管理',
    path: ERouter.API_MOCK_REPO_MGR,
    // forbiddenMenu: true,
    routeSettings: [
        {
            iconType: null,
            title: '项目管理',
            qsFields: [],
            path: ERouter.API_MOCK_REPO_MGR_PROJECT,
            importPromise: () => import('@/pages/ApiManager/ApiMenuEntry'),
        },
        {
            iconType: null,
            title: '仓库管理',
            qsFields: [],
            path: ERouter.API_MOCK_REPO_MGR,
            importPromise: () => import('@/pages/ApiManager/ApiMenuEntry'),
        },
        {
            iconType: null,
            title: 'API 视图',
            qsFields: ['bizId'],
            path: ERouter.API_MOCK_API_VIEW,
            importPromise: () => import('@/pages/ApiManager/apiViewEntry'),
        }
    ]
});

const routerApiTemplateSetting: RouteSetting = new RouteSetting({
    title: 'API 模板设置',
    path: ERouter.API_TEMPLATE_SETTING,
    menuKey: ERouter.API_MOCK_REPO_MGR,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/apiTemplateSetting'),
});

const routerRepoCreateTemplate: RouteSetting = new RouteSetting({
    title: '仓库模板',
    path: ERouter.API_MOCK_REPO_CREATE_TEMPLATE,
    menuKey: ERouter.API_TEMPLATE_SETTING,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/httpApi/createTemplate/CreateTemplate'),
});

// const routerRepoApiMgr: RouteSetting = new RouteSetting({
//     title: 'API 管理',
//     path: ERouter.API_MOCK_REPO_API_MGR,
//     menuKey: ERouter.API_MOCK_REPO_MGR,
//     forbiddenMenu: true,
//     importPromise: () => import('@/pages/ApiManager'),
// });

const routerRepoEditApi: RouteSetting = new RouteSetting({
    title: '编辑 API',
    path: ERouter.API_MOCK_REPO_EDIT_API,
    menuKey: ERouter.API_MOCK_REPO_MGR,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/httpApi/editApi'),
});

const routerApiVersionDiff: RouteSetting = new RouteSetting({
    title: '版本 DIFF',
    path: ERouter.API_MOCK_REPO_API_VERSION_DIFF,
    fullScreen: true,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/httpApi/apiVersionDiff'),
});

const routerTraceDiff: RouteSetting = new RouteSetting({
    title: 'trace DIFF',
    path: ERouter.API_MOCK_REPO_API_TRACE_DIFF,
    fullScreen: true,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/httpApi/traceDiff'),
});

const routerInsertApiDocs: RouteSetting = new RouteSetting({
    title: '插入 API 文档',
    path: ERouter.API_MOCK_REPO_API_DOCS,
    fullScreen: true,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/httpApi/insertApiDocs'),
});

const routerShowApiDocs: RouteSetting = new RouteSetting({
    title: '显示 API 文档',
    path: ERouter.API_MOCK_REPO_API_DOCS_SHOW,
    fullScreen: true,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/httpApi/showApiDocs'),
});

const routerBaseApiDocs: RouteSetting = new RouteSetting({
    title: '显示 API 文档',
    path: ERouter.API_MOCK_REPO_API_DOCS_ENTRY,
    fullScreen: true,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/httpApi/entryApiDocs'),
});

const routerMrCheckApi: RouteSetting = new RouteSetting({
    title: 'API 变更检查',
    path: ERouter.API_MOCK_REPO_MR_CHECK_API,
    forbiddenMenu: true,
    importPromise: () => import('@/pages/mrCheckApi')
});

const routers = [
    routerRepoMgr,
    routerApiTemplateSetting,
    routerShowApiDocs,
    // routerRepoApiMgr,
    routerRepoEditApi,
    routerApiVersionDiff,
    routerInsertApiDocs,
    routerRepoCreateTemplate,
    routerMrCheckApi,
    routerBaseApiDocs,
    routerTraceDiff
];

export default routers;
