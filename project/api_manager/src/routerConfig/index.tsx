import { ERouter } from 'CONFIG';
import { RouteSetting } from 'libs/a-component';
import { router } from '@libs/mvvm';

// import routerOrganizeSpace from './routerOrganizeSpace';
// import routerAutomaticParsingApi from './routerAutomaticParsingApi';
// import routetrProjectMgr from './routerProjectMgr';
// import routerVersionComparison from './routerVersionComparison';
import routerDemo from './routerDemo';
import routerApiMgr from './routerApiMgr';
import routerNewAutomaticParsingApi from './routerNewAutomaticParsingApi';
import routerApiTemplate from './routerTemplate';
import routerProxy from './routerProxy';
import routerHttpProxy, { projectInfoRouter, projectSettingRouter } from './routerHttpProxy';
import routerApiMgrProjectInfo from './routerApiMgrProjectInfo';
import routerApiMgrProjectSetting from './routerApiMgrProjectSetting';
import { EGray_Key, storeGrayFeatures } from '@kdev-fe-common/store';

const hiddenOldProxy = storeGrayFeatures.getValue(EGray_Key.hiddenOldProxy);

const newAllRouterSetting = [
    ...routerApiMgr,
    ...routerApiTemplate,
    ...routerApiMgrProjectInfo,
    ...routerApiMgrProjectSetting,
    ...(hiddenOldProxy ? [] : routerProxy),
    ...routerHttpProxy,
    ...routerNewAutomaticParsingApi,
];

const subMenu = [
    projectInfoRouter,
    projectSettingRouter,
];

// 隐藏老代理的人，访问老代理页面，自动定位到新代理
if (hiddenOldProxy && location.pathname === ERouter.API_MOCK_PROXY) {
    router.push(ERouter.API_MOCK_HTTP_PROXY);
}


export function hideHeader() {
    if (location.pathname === ERouter.API_MOCK_REPO_API_DOCS_ENTRY) {
        return true;
    }

    if (location.pathname === ERouter.API_MOCK_REPO_API_DOCS_SHOW) {
        return true;
    }

    if (location.pathname === ERouter.API_MOCK_REPO_API_DOCS) {
        return true;
    }

    if (location.pathname === ERouter.API_MOCK_REPO_API_VERSION_DIFF) {
        return true;
    }

    if (location.pathname === ERouter.API_MOCK_REPO_API_TRACE_DIFF) {
        return true;
    }

    if (location.pathname === ERouter.API_MOCK_REPO_API_TRACE_DIFF) {
        return true;
    }

    return false;
}

export function isShowSubMenu() {
    return [ERouter.API_MOCK_REPO_MGR_PROJECT_INFO, ERouter.API_MOCK_REPO_MGR_PROJECT_SETTING].includes(location.pathname);
}

export function filterRouter() {
    const showSubMenu = isShowSubMenu();
    const routerConfig = new RouteSetting({
        isRoot: true,
        path: ERouter.ROOT,
        routeSettings: showSubMenu ? subMenu : newAllRouterSetting
    });
    return routerConfig;
}
