import React, { useState, useEffect } from 'react';
import { withKDevPageError } from '@kdev-fe-common/common/errorHandler';
import zhCN from 'antd/es/locale/zh_CN';
import { ConfigProvider, Empty, Input, Popover, Tooltip } from 'antd';
import { filterRouter, hideHeader, isShowSubMenu } from './routerConfig';
import css from './app.less';
import { ERouter } from 'CONFIG';
import {
    GLOBAL,
    team,
    departmentComponent,
    departmentCascader,
} from '@/business/global';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import { DefaultEmptyIcon } from '@/business/commonIcon';
import {
    nsMockManageKoasApiManageNoticeGetNoticeGet,
    nsMockManageKoasApiManageNoticeCloseNoticeGet,
    nsApiManagerGroupTree,
    nsApiManagerList,
    nsApiManagerSearch,
    nsMockManageApiManageMainGroupDepartmentSearch,
    nsMockManageApiManageMainGroupUserDepartmentInfo
} from '@/remote';
import { kconfStore, weblogSendImmediatelyCustom } from '@/index.config';

import {
    KDevPageLayout,
    KDevPageHeaderRightSpace,
    HeaderUserCenter,
    HeaderKimOnCall,
    KDevPageSider,
    KDevPageTopMenu,
    HelpDocument,
    HeaderGlobalSearch,
} from '@kdev-fe-common/component/KDevPageLayout/KDevPageLayout';
import { router } from 'kdev-fe-common/src/shared/libs';
import { common_system_right_arrow_surface, common_system_search, common_system_xiaojiantouyou } from '@kid/enterprise-icon/icon/output/icons';
import { KdevIconFont, KEmpty } from '@/business/commonComponents';
import { RepoAvatar } from './business/repo/RepoAvatar';
import { pushKey } from './index.config/tools';
import { Debounce } from 'lodash-decorators/debounce';
import { KDEV_API_GROUP, KDEV_API_MENU_SELECTEDKEYS, KDEV_API_SEARCH_GROUP } from './pages/ApiManager/apiManager.config';
import { getUrlSearch } from '@/index.config/tools'
import { Route, Switch } from 'react-router';
import { AsyncLoader } from 'libs/a-component/AsyncLoader';
import { QuickAccess } from './pages/ApiManager/ProjectView/QuickAccess/QuickAccess';
import { EGray_Key, storeGrayFeatures } from '@kdev-fe-common/store';


interface IState {
    noticMsg: string;
    noticeUrl: string;
    searchKey: string;
    groupList: nsApiManagerSearch.IItem[];
    selectedGroup: number;
    hoverGroup: number;
    selectedSubGroup: number,
    userGroup?: nsApiManagerSearch.IItem;
    popoverOpen: boolean;
    isSearch: boolean,
    searchList: nsMockManageApiManageMainGroupDepartmentSearch.IItem[],
    hideNavMenu: boolean,
    firstDepartmentGroupId: number,
    search: boolean
}

declare global {
    interface Window {
        search: boolean;
    }
}

// 判断两个元素是否相交
function getRelativePos(oneNode: Element, twoNode: Element) {
    const oneNodeRect = oneNode.getBoundingClientRect();
    const twoNodeRect = twoNode.getBoundingClientRect();
    return twoNodeRect.top - oneNodeRect.top
}


const isNeedLayout = (): boolean => {
    const notNeedLayout = [
        ERouter.API_MOCK_REPO_API_DOCS,
        ERouter.API_MOCK_REPO_API_DOCS_SHOW,
        ERouter.API_MOCK_REPO_API_DOCS_ENTRY,
    ]
    return notNeedLayout.includes(location.pathname as ERouter);
}

@observer
class AppTaxiOps extends React.Component<any, IState> {
    private unListenRouter: any;
    private groupListRef = React.createRef<HTMLDivElement>();
    private subGroupListRef = React.createRef<HTMLDivElement>();
    private hoverTimer: NodeJS.Timeout | null = null;
    public state: IState = {
        noticMsg: '',
        noticeUrl: '',
        searchKey: '',
        groupList: [],
        selectedGroup: 0,
        hoverGroup: 0,
        selectedSubGroup: 0,
        userGroup: undefined,
        popoverOpen: false,
        isSearch: false,
        searchList: [],
        hideNavMenu: false,
        firstDepartmentGroupId: 0,
        search: false
    };
    constructor(props: any) {
        super(props);
        const url = getUrlSearch() as { search: string };
        window.search = Boolean(url.search);
    }

    @Bind
    private async addGroupCb(info): Promise<void> {
        info.detail.ancestorsId.children = [];
        const addGroup = info.detail.ancestorsId;
        this.setState({
            userGroup: addGroup,
            selectedGroup: addGroup.groupId,
        }, () => {
            pushKey({ groupId: info.detail.parentGroupId });
            localStorage.setItem(KDEV_API_GROUP, JSON.stringify(addGroup));
            document.dispatchEvent(new CustomEvent('selectedGroup', {
                // detail: {
                //     groupId: info.detail.parentGroupId
                // }
            }))
            this.getSubGroup();
        });
    }

    @Bind
    private async changeHideNavMenu(info): Promise<void> {
        this.setState({
            hideNavMenu: info.detail.hideNavMenu
        })
    }

    public async componentDidMount(): Promise<void> {
        this.getNotic();
        await this.getGroup();
        this.getSubGroup();

        setTimeout(() => {
            weblogSendImmediatelyCustom('SHOW_EVENT', { id: 'new_kdev_nav_v2' })
        }, 2000)

        this.unListenRouter = router.routerProps.history.listen(() => {
            // 需要重新计算 顶部 和 侧边导航
            this.setState({});
        });
        document.addEventListener('addGroup', this.addGroupCb);
        document.addEventListener('changeHideNavMenu', this.changeHideNavMenu);
        const url = getUrlSearch() as { hideNavMenu: string, firstDepartmentGroupId: string, search: string };
        if (url.hideNavMenu === 'true') {
            this.setState({
                hideNavMenu: true,
            });
        }

        this.setState({
            firstDepartmentGroupId: Number(url.firstDepartmentGroupId),
            search: Boolean(url.search)
        })

        if (this.state.firstDepartmentGroupId) {
            this.handleItemClick(this.state.groupList.find(i => i.groupId === this.state.firstDepartmentGroupId))
        }

        if (url.search) {
            window.search = true;
        }
    }

    public componentWillUnmount(): void {
        this.unListenRouter?.()
        document.removeEventListener('addGroup', this.addGroupCb);
        document.removeEventListener('changeHideNavMenu', this.changeHideNavMenu);
        if (this.hoverTimer) {
            clearTimeout(this.hoverTimer);
            this.hoverTimer = null;
        }
    }

    @Bind
    private async getNotic(): Promise<void> {
        try {
            const result = await nsMockManageKoasApiManageNoticeGetNoticeGet.remote();
            this.setState({ noticMsg: result?.notice, noticeUrl: result?.url });
        } catch { }
    }

    // 获取一级分组（加载时执行一次）
    @Bind
    private async getGroup(): Promise<void> {
        try {
            // 获取上次选择的分组
            const userLastGroupStr = localStorage.getItem(KDEV_API_GROUP) || '';

            let userLastGroup: nsApiManagerSearch.IItem
            // 没有上次的分组就获取用户所在分组
            if (!userLastGroupStr) {
                const res = await nsMockManageApiManageMainGroupUserDepartmentInfo.remote({});
                res.secondDepartmentGroup.groupName =
                    res.firstDepartmentGroup.groupName + '/' + res.secondDepartmentGroup.groupName
                userLastGroup = res.secondDepartmentGroup
            } else {
                userLastGroup = JSON.parse(userLastGroupStr);
            }

            if (userLastGroup.parentId === -1) { // 如果是一级目录
                this.setState({
                    selectedGroup: userLastGroup.groupId,
                    userGroup: userLastGroup
                });
            } else if (userLastGroup.parentId > 0) { // 如果是二级目录
                this.setState({
                    selectedGroup: userLastGroup.parentId,
                    selectedSubGroup: userLastGroup.groupId,
                    userGroup: userLastGroup
                });
            }
            // 获取全部一级分组
            const result = await nsApiManagerGroupTree.remote({});
            result.list.forEach(i => { i.key = '' + i.groupId })
            this.setState({
                groupList: result.list,
            }, () => {
                localStorage.setItem(KDEV_API_GROUP, JSON.stringify(this.state.userGroup));
            });
        } catch { }
    }

    // 获取二级分组（点击一级分组后执行一次）
    @Bind
    private async getSubGroup(): Promise<void> {
        try {
            const groupId = this.state.hoverGroup || this.state.selectedGroup;
            const { item } = await nsApiManagerList.remote({
                groupId
            });
            item.children = item.children?.filter(i => i.groupId);
            const index = this.state.groupList.findIndex(i => i.groupId === groupId);
            this.state.groupList[index] = item;
            this.setState({
                groupList: [...this.state.groupList],
            });
        } catch { }
    }

    @Bind
    private async onCloseNotice(): Promise<void> {
        try {
            await nsMockManageKoasApiManageNoticeCloseNoticeGet.remote();
        } catch { }
    }


    private handleItemClick = (item) => {
        this.setState({
            selectedGroup: item.groupId,
            selectedSubGroup: 0,
            userGroup: item
        }, () => {
            this.getSubGroup()
        });
        // pushKey({ groupId: '', apiId: '' })

        if (this.state.search) {
            localStorage.setItem(KDEV_API_SEARCH_GROUP, JSON.stringify(item));
        } else {
            localStorage.setItem(KDEV_API_GROUP, JSON.stringify(item));
        }
        // localStorage.setItem(KDEV_API_GROUP, JSON.stringify(item));
        // localStorage.removeItem(KDEV_API_MENU_SELECTEDKEYS);
        document.dispatchEvent(new CustomEvent('selectedGroup'))
    }

    private renderHeaderRight() {
        return (
            <KDevPageHeaderRightSpace>
                <HeaderGlobalSearch />
                {/*oncall*/}
                <HeaderKimOnCall
                    userName={team.model.userName}
                    filterPath={(path) => path === '/web/api-mock'}
                />
                {/* 帮助文档 */}
                <HelpDocument
                    localList={[
                        {
                            id: 1,
                            name: '帮助文档',
                            url: kconfStore.isShowNewVersion
                                ? 'https://docs.corp.kuaishou.com/d/home/<USER>'
                                : 'https://docs.corp.kuaishou.com/d/home/<USER>',
                            iconUrl:
                                'https://h1.static.yximgs.com/udata/pkg/QA-SERVEREE-FE-OUT/kdev_help_v2_icon.svg',
                        },
                    ]}
                />
                {/*用户中心*/}
                <HeaderUserCenter
                    photo={team.model.photo}
                    chineseName={team.model.chineseName}
                />
            </KDevPageHeaderRightSpace>
        );
    }

    private getCurrentSelectedGroup() {
        const groupId = this.state.hoverGroup;
        return this.state.groupList.find(
            item => item.groupId === groupId
        )
    }

    @Debounce(200)
    private async onSearch() {
        this.setState({
            isSearch: !!this.state.searchKey.trim()
        })
        const searchRes = await nsMockManageApiManageMainGroupDepartmentSearch.remote(
            { key: this.state.searchKey }
        );
        this.setState({
            searchList: searchRes
        })
    }

    renderPopoverContent() {
        return (
            <div className={css.bizSelectPopover}>
                <Input.Search
                    allowClear
                    className={[css.bizSelectSearch,
                    this.state.isSearch ? css.isSearch : ''].join(' ')}
                    placeholder={'搜索空间关键词'}
                    prefix={
                        <KdevIconFont
                            id={common_system_search}
                            style={{ color: '#D5D6D9' }}
                        />
                    }
                    value={this.state.searchKey}
                    onChange={async (event) => {
                        this.setState({
                            searchKey: event.target.value,
                            isSearch: !!event.target.value.trim()
                        });
                        this.onSearch();
                    }}
                />
                {
                    this.state.isSearch ?
                        <div className={css.searchList}>
                            {this.state.searchList.length ?
                                this.state.searchList.map((item, index) => (
                                    <p
                                        className={css.searchItem}
                                        key={index}
                                        dangerouslySetInnerHTML={{
                                            __html: item.groupPath.replace(
                                                new RegExp(this.state.searchKey.trim(), 'g'),
                                                (v) => `<span style="color: #326BFB">${v}</span>`
                                            )
                                        }}
                                        onClick={() => {
                                            const userGroup = item.secondDepartmentGroup ?
                                                {
                                                    ...item.secondDepartmentGroup,
                                                    groupName: `${item.firstDepartmentGroup.groupName}/${item.secondDepartmentGroup.groupName}`
                                                } : item.firstDepartmentGroup
                                            this.setState({
                                                selectedSubGroup: item.secondDepartmentGroup?.groupId || 0,
                                                selectedGroup: item.firstDepartmentGroup.groupId,
                                                popoverOpen: false,
                                                isSearch: false,
                                                searchKey: '',
                                                userGroup: userGroup
                                            });
                                            localStorage.setItem(KDEV_API_GROUP, JSON.stringify(
                                                userGroup
                                            ));
                                            document.dispatchEvent(new CustomEvent('selectedGroup'))
                                        }}
                                    />
                                )) :
                                <KEmpty></KEmpty>}
                        </div>
                        :
                        <div className={css.bizOptionsBox}>
                            <div className={[css.left, 'scroll-hover'].join(' ')} ref={this.groupListRef}>
                                {
                                    this.state.groupList.map((item, index) => {
                                        return <div
                                            key={index}
                                            className={[
                                                css.group1,
                                                item.groupId === this.state.selectedGroup ? css.selected : ''
                                            ].join(' ')}
                                            onClick={async () => {
                                                this.setState({
                                                    selectedGroup: item.groupId,
                                                    selectedSubGroup: 0,
                                                    userGroup: item,
                                                    popoverOpen: false
                                                });
                                                pushKey({ groupId: '', apiId: '' })
                                                if (window.search) {
                                                    localStorage.setItem(KDEV_API_SEARCH_GROUP, JSON.stringify(item));
                                                } else {
                                                    localStorage.setItem(KDEV_API_GROUP, JSON.stringify(item));
                                                }
                                                localStorage.removeItem(KDEV_API_MENU_SELECTEDKEYS);
                                                document.dispatchEvent(new CustomEvent('selectedGroup'))
                                            }}
                                            onMouseEnter={() => {
                                                this.hoverTimer = setTimeout(() => {
                                                    this.setState({
                                                        hoverGroup: item.groupId,
                                                    }, () => {
                                                        this.getSubGroup()
                                                    });
                                                }, 500);
                                            }}
                                            onMouseLeave={() => {
                                                if (this.hoverTimer) {
                                                    clearTimeout(this.hoverTimer);
                                                    this.hoverTimer = null;
                                                }
                                            }}
                                        >
                                            <RepoAvatar
                                                name={item.groupName}
                                                className={css.avatar}
                                                size={24}
                                            />
                                            <Tooltip title={item.groupName} mouseEnterDelay={0.3}>
                                                <div className={css.groupName}>
                                                    {item.groupName}
                                                </div>
                                            </Tooltip>
                                            <KdevIconFont
                                                className={css.rightArrow}
                                                id={common_system_xiaojiantouyou}
                                                style={{ color: '#D5D6D9' }}
                                            />
                                        </div>
                                    })
                                }
                            </div>
                            <div className={css.line}></div>
                            <div className={[css.right, 'scroll-hover'].join(' ')} ref={this.subGroupListRef}>
                                {
                                    this.getCurrentSelectedGroup()?.children?.map((item, index) => (
                                        <div
                                            key={index}
                                            className={[
                                                css.group2,
                                                item.groupId === this.state.selectedSubGroup ? css.selected : ''
                                            ].join(' ')}
                                            onClick={async () => {
                                                const subGroup = {
                                                    ...item,
                                                    groupName: `${this.getCurrentSelectedGroup()?.groupName}/${item.groupName}`
                                                }
                                                this.setState({
                                                    selectedSubGroup: item.groupId,
                                                    userGroup: subGroup,
                                                    popoverOpen: false
                                                });
                                                pushKey({ groupId: '', apiId: '' })
                                                localStorage.setItem(KDEV_API_GROUP, JSON.stringify(subGroup));
                                                localStorage.removeItem(KDEV_API_MENU_SELECTEDKEYS);
                                                document.dispatchEvent(new CustomEvent('selectedGroup'))
                                            }}
                                        >
                                            <Tooltip title={item.groupName} mouseEnterDelay={0.3}>
                                                <div className={css.groupName}>
                                                    {item.groupName}
                                                </div>
                                            </Tooltip>
                                        </div>
                                    ))
                                }
                            </div>
                        </div>
                }
            </div>
        );
    }

    @Bind
    renderDepartment(collapse: boolean) {
        if (
            departmentComponent.departmentComponentModel.hidden
            && departmentCascader.departmentCascaderModel.hidden
        ) {
            return <Popover
                content={this.renderPopoverContent()}
                placement="bottom"
                title={null}
                trigger="click"
                overlayClassName={css.menuOverlay}
                destroyTooltipOnHide
                open={this.state.popoverOpen}
                onOpenChange={(newOpen) => {
                    this.setState({
                        popoverOpen: newOpen,
                    }, () => {
                        if (newOpen) {
                            setTimeout(() => {
                                const selected = this.groupListRef.current?.querySelector('.' + css.selected)
                                this.groupListRef.current?.scrollTo({
                                    top: getRelativePos(this.groupListRef.current, selected!)
                                } as any);

                                const subSelected = this.subGroupListRef.current?.querySelector('.' + css.selected)
                                this.subGroupListRef.current?.scrollTo({
                                    top: getRelativePos(this.subGroupListRef.current, subSelected!)
                                } as any);
                            }, 0)
                        }
                    })
                    if (!newOpen) {
                        this.setState({
                            isSearch: false,
                            searchKey: ''
                        })
                    }
                }}
            >
                {
                    isShowSubMenu() ? <>
                        <div className={css.backProjectList}>
                            <KdevIconFont id={common_system_right_arrow_surface} style={{
                                marginRight: '4px'
                            }} />
                            <span>返回项目列表</span>
                        </div>
                    </> : <></>
                }

            </Popover>
        }

        return (
            <div className={css.globalGroupV2}>
                {!departmentComponent.departmentComponentModel.hidden && (
                    <span>{departmentComponent.renderSelect(collapse)}</span>
                )}
                {!departmentCascader.departmentCascaderModel.hidden && (
                    <span>{departmentCascader.renderSelect(collapse)}</span>
                )}
            </div>
        );
    }

    @Bind
    renderQuickAccess() {
        return <QuickAccess />
    }

    @Bind
    private renderSider() {
        const routerConfig =
            location.pathname !== ERouter.API_MOCK_PROXY &&
            location.pathname !== ERouter.API_MOCK_REPO_API_VERSION_DIFF &&
            filterRouter();
        if (!routerConfig) {
            return null;
        }
        return <KDevPageSider
            routeSetting={routerConfig}
            menuHeader={this.renderDepartment}
            additional={storeGrayFeatures.getValue(EGray_Key.projectView)
                ? this.renderQuickAccess
                : () => <></>
            }
        />;
    }

    @Bind
    private renderEmptyDepartment(): React.ReactNode {
        return (
            <Empty
                image={<DefaultEmptyIcon />}
                description="请在左上角选择部门"
                className={css.defaultEmpty}
            />
        );
    }

    private renderNoLayout = () => {
        const proNotFound = () => import('@pages/gateway/NotFoundPage');
        return <Switch>
            {
                GLOBAL.loaded()
                    ? filterRouter().renderRoutes()
                    : this.renderEmptyDepartment()
            }
            <Route
                path="*"
                render={
                    // 路由全部不匹配：页面不存在
                    () => (
                        <AsyncLoader
                            compoLazy={proNotFound}
                            fallback={<div />}
                        />
                    )
                }
            />
        </Switch>
    }

    public renderContent(): React.ReactNode {

        const center = (
            <KDevPageTopMenu
                getKeyByPathName={() => {
                    return '/web/api-mock';
                }}
            />
        );

        const noLayout = <this.renderNoLayout />;

        return isNeedLayout() ? noLayout : (
            <KDevPageLayout
                header={hideHeader() || this.state.hideNavMenu ? null : {
                    right: this.renderHeaderRight(),
                    center: center,
                }}
                slider={this.state.hideNavMenu ? null : this.renderSider()}
            >
                {noLayout}
            </KDevPageLayout>
        );
    }

    public render(): React.ReactNode {
        return (
            <ConfigProvider locale={zhCN} renderEmpty={() => <KEmpty image="NOMAL_SIMPLE_SEARCH_2" />}>
                {kconfStore.isLoaded && this.renderContent()}
            </ConfigProvider>
        );
    }
}

export default withKDevPageError(AppTaxiOps);
