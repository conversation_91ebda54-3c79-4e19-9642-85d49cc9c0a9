import { AView } from 'libs';
import React from 'react';
import { observer } from 'mobx-react';
import { Bind } from 'lodash-decorators';
import css from './TestResult.less';
import { Modal, Button, Tooltip, Input } from 'antd';
import { Tips, testCaseStatus } from './configure';
import { TestResultM } from './TestResultM';
import { KdevIconFont } from '@/business/commonComponents/KdevIconFont';
import { RequestParams } from '../requestParams/RequestParams';
import { ResponseParams } from '../responseParams/ResponseParams';
import { AceDrag } from '@/business/aceDrag/AceDrag';

@observer
export class TestResult extends AView<TestResultM> {

    @Bind
    private renderReqResTips(tips: string): React.ReactNode {
        return (
            <Tooltip title={tips}>
                <KdevIconFont id={'#iconquestion'} className={css.tipsIcon} />
            </Tooltip>
        );
    }

    @Bind
    private renderCaseStatus(): React.ReactNode {
        const model = this.model;
        return (
            <div className={css.baseInfoRow}>
                <span>测试结果：</span>
                <span style={{ color: testCaseStatus[model.status]?.color }}>
                    {testCaseStatus[model.status]?.statusDesc}
                    {model.remark && this.renderReqResTips(model.remark)}
                    {
                        model.status !== 3 &&
                        <Button
                            size={'small'}
                            type={'link'}
                            className={css.ignoreBtn}
                            onClick={model.ignoreCase}
                        >
                            忽略
                            {this.renderReqResTips(Tips['result'])}
                        </Button>
                    }
                </span>
            </div>
        );
    }

    @Bind
    private renderRequestParams(): React.ReactNode {
        const model = this.model;
        if (model.showRequest) {
            return (
                <>
                    <div className={css.baseInfoRow}>
                        <span>请求参数</span>
                        {/* {this.renderReqResTips('reqTips')} */}
                    </div>
                    <RequestParams model={model.requestParamsM} />
                </>
            );
        }
    }

    @Bind
    private renderResponseParams(): React.ReactNode {
        const model = this.model;
        if (model.showResponse && model.resDataType === 1) {
            return (
                <>
                    <div className={css.baseInfoRow}>
                        返回参数
                        <span className={css.dataType}>静态JSON</span>
                        <span className={css.dataType}>
                            {model.ruleType === 2 ? '数据结构校验' : '返回值校验'}
                        </span>
                    </div>
                    <ResponseParams model={model.responseParamsM} />
                </>
            );
        }
        if (model.resDataType === 2 && model.resPythonScript) {
            return (
                <>
                    <div className={css.baseInfoRow}>
                        <span>
                            返回参数
                            <span className={css.dataType}>Python2.7 脚本</span>
                        </span>
                    </div>
                    <AceDrag
                        theme={'tomorrow'}
                        mode={'python'}
                        readOnly
                        value={model.resPythonScript}
                        aceDragClassName={css.example}
                    />
                </>
            );
        }
    }

    @Bind
    private renderCurl(): React.ReactNode {
        const { curl } = this.model;
        if (curl) {
            return (
                <>
                    <div className={css.baseInfoRow}>curl</div>
                    <Input.TextArea readOnly value={curl} className={css.curl} rows={10} />
                </>
            );
        }
    }

    public render(): React.ReactNode {
        const model = this.model;
        return (
            <Modal
                className={css.testResultModal}
                visible={model.visible}
                width={'80%'}
                title={'测试结果'}
                onCancel={model.onCloseTestConfigurationModal}
                okText="测试"
                onOk={model.doTestCase}
                okButtonProps={{
                    loading: model.doTestCaseLoading
                }}
            >
                <div className={css.testResultModalBody}>
                    <div className={css.baseInfoRow}><span>用例名称：</span>{model.caseName}</div>
                    <div className={css.baseInfoRow}><span>用例描述：</span>{model.caseDesc}</div>
                    {this.renderCaseStatus()}
                    <div className={css.baseInfoRow}><span>测试时间：</span>{model.caseExecuteTime}</div>
                    {this.renderRequestParams()}
                    <div className={css.baseInfoRow}>请求示例</div>
                    <AceDrag readOnly value={model.reqBodyExample} aceDragClassName={css.example} />
                    {this.renderResponseParams()}
                    <div className={css.baseInfoRow}>{model.type === 'koasCaseId' ? '实际返回值' : '返回示例'}</div>
                    <AceDrag readOnly value={model.resBodyExample} aceDragClassName={css.example} />
                    {this.renderCurl()}
                </div>
            </Modal>
        );
    }
}
